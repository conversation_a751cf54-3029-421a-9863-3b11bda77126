// 模块化游戏主文件
import { GameEngine } from './modules/GameEngine.js';

// 全局游戏实例
let game;

// 初始化游戏
window.onload = function() {
    try {
        game = new GameEngine();

        // 将游戏实例暴露到全局作用域，供HTML按钮调用
        window.game = game;

        // 尝试加载存档
        if (localStorage.getItem('apocalypseRVSave')) {
            if (confirm("发现存档，是否继续上次的游戏？")) {
                game.loadGame();
            }
        }

        console.log('模块化游戏初始化成功');
    } catch (error) {
        console.error('游戏初始化失败:', error);
        alert('游戏初始化失败，请刷新页面重试');
    }
};

// 全局函数，供HTML调用
window.exploreLocation = function() {
    if (game) game.exploreLocation();
};

window.moveToNextLocation = function() {
    if (game) game.moveToNextLocation();
};

window.restAtLocation = function() {
    if (game) game.restAtLocation();
};

window.openMapPanel = function() {
    if (game) game.openMapPanel();
};

window.closeMapPanel = function() {
    if (game) game.closeMapPanel();
};

window.changeRegionUp = function() {
    if (game) game.changeRegionUp();
};

window.changeRegionDown = function() {
    if (game) game.changeRegionDown();
};

window.openUpgradePanel = function() {
    if (game) game.openUpgradePanel();
};

window.closeUpgradePanel = function() {
    if (game) game.closeUpgradePanel();
};

window.attackEnemy = function() {
    if (game) game.attackEnemy();
};

window.defendAction = function() {
    if (game) game.defendAction();
};

window.fleeFromCombat = function() {
    if (game) game.fleeFromCombat();
};

window.toggleSound = function() {
    if (game) game.toggleSound();
};

window.openMemoryPanel = function() {
    if (game) game.openMemoryPanel();
};

window.closeMemoryPanel = function() {
    if (game) game.closeMemoryPanel();
};

window.openCraftingPanel = function() {
    if (game) game.openCraftingPanel();
};

window.closeCraftingPanel = function() {
    if (game) game.closeCraftingPanel();
};

window.craftItem = function(category, itemId) {
    if (game) game.craftItem(category, itemId);
};

window.unequipItem = function(slot) {
    if (game) game.unequipItem(slot);
};



window.useConsumable = function(itemId) {
    if (game) game.useConsumable(itemId);
};

// 错误处理
window.addEventListener('error', function(event) {
    console.error('游戏运行错误:', event.error);
});

// 模块加载错误处理
window.addEventListener('unhandledrejection', function(event) {
    console.error('模块加载错误:', event.reason);
    alert('游戏模块加载失败，请检查浏览器是否支持ES6模块');
});

export { game };
