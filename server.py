#!/usr/bin/env python3
import http.server
import socketserver
import webbrowser
import threading
import time
import os

# 设置端口
PORT = 8000

class CORSRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()

def open_browser():
    """延迟打开浏览器"""
    time.sleep(2)
    webbrowser.open(f'http://localhost:{PORT}/index.html')

if __name__ == "__main__":
    # 切换到脚本所在目录
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    print(f"🚐 末日房车游戏服务器")
    print(f"📡 地址: http://localhost:{PORT}")
    print(f"🎮 原版本: http://localhost:{PORT}/index.html")
    print(f"\n💡 使用 Ctrl+C 停止服务器")
    print(f"⚡ 服务器启动中...")
    
    # 启动浏览器线程
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()
    
    # 启动服务器
    try:
        with socketserver.TCPServer(("", PORT), CORSRequestHandler) as httpd:
            print(f"✅ 服务器运行在端口 {PORT}")
            httpd.serve_forever()
    except KeyboardInterrupt:
        print(f"\n🛑 服务器已停止")
    except Exception as e:
        print(f"❌ 错误: {e}")
        input("按回车键退出...")
